﻿[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] ========== 开始参数获取测试 ==========
[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] 输入参数 - dispensary: 110202
[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] 系统参数 - LoginUser.EMP_NO: 0073
[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] 系统参数 - HisUnitCode: 45038900950011711A6001
[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] 参数查询结果: '1'
[2025-08-28 11:54:48] [库存验证] [TestParameterRetrieval] ========== 参数获取测试结束 ==========
[2025-08-28 11:54:48] [库存验证] [ValidateStock] ========== 库存验证请求 ==========
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 药品代码：63020013YP1
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 药品名称：天麻
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 药品规格：1g
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 厂家ID：重庆康嘉
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 药房：110202
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 需要数量：150克
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 当前用户：0073
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 医院代码：45038900950011711A6001
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 正在检查统一库存验证参数...
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE=110202, EMP_NO=0073, HIS_UNIT_CODE=45038900950011711A6001
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 参数查询结果：1
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 开始统一库存验证 - 药品：天麻(63020013YP1)，需要数量：150克
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 正在查询药品库存 - 药品：63020013YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 开始查询库存 - 药品：63020013YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 创建数据库连接...
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 构建SQL查询...
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] SQL语句：SELECT NVL(SUM(QUANTITY), 0)  FROM DRUG_STOCK   WHERE DRUG_CODE = :DRUGCODE  AND PACKAGE_SPEC = :DRUGSPEC  AND FIRM_ID = :FRIMID AND STORAGE = :DISPENSARY AND SUPPLY_INDICATOR = '1'  AND HIS_UNIT_CODE = :HIS_UNIT_CODE
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 设置查询参数...
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 参数值 - DRUGCODE:63020013YP1, DRUGSPEC:1g, FRIMID:重庆康嘉, DISPENSARY:110202, HIS_UNIT_CODE:45038900950011711A6001
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 执行数据库查询...
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 查询执行完成
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 处理查询结果...
[2025-08-28 11:54:48] [库存验证] [GetDrugInventoryInStorage] 查询成功 - 药品：63020013YP1，库存数量：2906
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 库存查询结果 - 药品：63020013YP1，库存数量：2906
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 临时简化验证 - 药品：天麻(63020013YP1)，物理库存：2906，预扣库存：0（已禁用），可用库存：2906
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 库存验证通过 - 药品：天麻(63020013YP1)
[2025-08-28 11:54:48] [库存验证] [ValidateStock] 物理库存：2906.00克，预扣库存：0.00克，可用库存：2906.00克，需要数量：150.00克
[2025-08-28 11:54:48] [库存验证] [ValidateStock] ========== 验证结束（成功）==========
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] ========== 开始参数获取测试 ==========
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 输入参数 - dispensary: 110202
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 系统参数 - LoginUser.EMP_NO: 0073
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 系统参数 - HisUnitCode: 45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 参数查询结果: '1'
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] ========== 参数获取测试结束 ==========
[2025-08-28 11:54:49] [库存验证] [ValidateStock] ========== 库存验证请求 ==========
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品代码：63080250YP1
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品名称：陈皮
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品规格：1g
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 厂家ID：重庆康嘉
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药房：110202
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 需要数量：120克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 当前用户：0073
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 医院代码：45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 正在检查统一库存验证参数...
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE=110202, EMP_NO=0073, HIS_UNIT_CODE=45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 参数查询结果：1
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 开始统一库存验证 - 药品：陈皮(63080250YP1)，需要数量：120克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 正在查询药品库存 - 药品：63080250YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 开始查询库存 - 药品：63080250YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 创建数据库连接...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 构建SQL查询...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] SQL语句：SELECT NVL(SUM(QUANTITY), 0)  FROM DRUG_STOCK   WHERE DRUG_CODE = :DRUGCODE  AND PACKAGE_SPEC = :DRUGSPEC  AND FIRM_ID = :FRIMID AND STORAGE = :DISPENSARY AND SUPPLY_INDICATOR = '1'  AND HIS_UNIT_CODE = :HIS_UNIT_CODE
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 设置查询参数...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 参数值 - DRUGCODE:63080250YP1, DRUGSPEC:1g, FRIMID:重庆康嘉, DISPENSARY:110202, HIS_UNIT_CODE:45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 执行数据库查询...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 查询执行完成
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 处理查询结果...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 查询成功 - 药品：63080250YP1，库存数量：421
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 库存查询结果 - 药品：63080250YP1，库存数量：421
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 临时简化验证 - 药品：陈皮(63080250YP1)，物理库存：421，预扣库存：0（已禁用），可用库存：421
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 库存验证通过 - 药品：陈皮(63080250YP1)
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 物理库存：421.00克，预扣库存：0.00克，可用库存：421.00克，需要数量：120.00克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] ========== 验证结束（成功）==========
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] ========== 开始参数获取测试 ==========
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 输入参数 - dispensary: 110202
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 系统参数 - LoginUser.EMP_NO: 0073
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 系统参数 - HisUnitCode: 45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] 参数查询结果: '1'
[2025-08-28 11:54:49] [库存验证] [TestParameterRetrieval] ========== 参数获取测试结束 ==========
[2025-08-28 11:54:49] [库存验证] [ValidateStock] ========== 库存验证请求 ==========
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品代码：63080334YP1
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品名称：川贝母
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药品规格：1g
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 厂家ID：重庆康嘉
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 药房：110202
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 需要数量：50克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 当前用户：0073
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 医院代码：45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 正在检查统一库存验证参数...
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE=110202, EMP_NO=0073, HIS_UNIT_CODE=45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 参数查询结果：1
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 开始统一库存验证 - 药品：川贝母(63080334YP1)，需要数量：50克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 正在查询药品库存 - 药品：63080334YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 开始查询库存 - 药品：63080334YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 创建数据库连接...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 构建SQL查询...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] SQL语句：SELECT NVL(SUM(QUANTITY), 0)  FROM DRUG_STOCK   WHERE DRUG_CODE = :DRUGCODE  AND PACKAGE_SPEC = :DRUGSPEC  AND FIRM_ID = :FRIMID AND STORAGE = :DISPENSARY AND SUPPLY_INDICATOR = '1'  AND HIS_UNIT_CODE = :HIS_UNIT_CODE
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 设置查询参数...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 参数值 - DRUGCODE:63080334YP1, DRUGSPEC:1g, FRIMID:重庆康嘉, DISPENSARY:110202, HIS_UNIT_CODE:45038900950011711A6001
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 执行数据库查询...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 查询执行完成
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 处理查询结果...
[2025-08-28 11:54:49] [库存验证] [GetDrugInventoryInStorage] 查询成功 - 药品：63080334YP1，库存数量：1468
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 库存查询结果 - 药品：63080334YP1，库存数量：1468
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 临时简化验证 - 药品：川贝母(63080334YP1)，物理库存：1468，预扣库存：0（已禁用），可用库存：1468
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 库存验证通过 - 药品：川贝母(63080334YP1)
[2025-08-28 11:54:49] [库存验证] [ValidateStock] 物理库存：1468.00克，预扣库存：0.00克，可用库存：1468.00克，需要数量：50.00克
[2025-08-28 11:54:49] [库存验证] [ValidateStock] ========== 验证结束（成功）==========
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] ========== 开始参数获取测试 ==========
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 输入参数 - dispensary: 110202
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 系统参数 - LoginUser.EMP_NO: 0073
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 系统参数 - HisUnitCode: 45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 参数查询结果: '1'
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] ========== 参数获取测试结束 ==========
[2025-08-28 11:54:50] [库存验证] [ValidateStock] ========== 库存验证请求 ==========
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品代码：63080046YP1
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品名称：麸炒苍术
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品规格：1g
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 厂家ID：重庆康嘉
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药房：110202
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 需要数量：150克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 当前用户：0073
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 医院代码：45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 正在检查统一库存验证参数...
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE=110202, EMP_NO=0073, HIS_UNIT_CODE=45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 参数查询结果：1
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 开始统一库存验证 - 药品：麸炒苍术(63080046YP1)，需要数量：150克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 正在查询药品库存 - 药品：63080046YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 开始查询库存 - 药品：63080046YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 创建数据库连接...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 构建SQL查询...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] SQL语句：SELECT NVL(SUM(QUANTITY), 0)  FROM DRUG_STOCK   WHERE DRUG_CODE = :DRUGCODE  AND PACKAGE_SPEC = :DRUGSPEC  AND FIRM_ID = :FRIMID AND STORAGE = :DISPENSARY AND SUPPLY_INDICATOR = '1'  AND HIS_UNIT_CODE = :HIS_UNIT_CODE
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 设置查询参数...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 参数值 - DRUGCODE:63080046YP1, DRUGSPEC:1g, FRIMID:重庆康嘉, DISPENSARY:110202, HIS_UNIT_CODE:45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 执行数据库查询...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 查询执行完成
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 处理查询结果...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 查询成功 - 药品：63080046YP1，库存数量：1011
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 库存查询结果 - 药品：63080046YP1，库存数量：1011
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 临时简化验证 - 药品：麸炒苍术(63080046YP1)，物理库存：1011，预扣库存：0（已禁用），可用库存：1011
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 库存验证通过 - 药品：麸炒苍术(63080046YP1)
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 物理库存：1011.00克，预扣库存：0.00克，可用库存：1011.00克，需要数量：150.00克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] ========== 验证结束（成功）==========
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] ========== 开始参数获取测试 ==========
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 输入参数 - dispensary: 110202
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 系统参数 - LoginUser.EMP_NO: 0073
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 系统参数 - HisUnitCode: 45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] 参数查询结果: '1'
[2025-08-28 11:54:50] [库存验证] [TestParameterRetrieval] ========== 参数获取测试结束 ==========
[2025-08-28 11:54:50] [库存验证] [ValidateStock] ========== 库存验证请求 ==========
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品代码：63040001YP1
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品名称：菊花
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药品规格：1g
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 厂家ID：重庆康嘉
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 药房：110202
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 需要数量：120克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 当前用户：0073
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 医院代码：45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 正在检查统一库存验证参数...
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 查询参数：APP_NAME=PRESDISP, DEPT_CODE=110202, EMP_NO=0073, HIS_UNIT_CODE=45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 参数查询结果：1
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 开始统一库存验证 - 药品：菊花(63040001YP1)，需要数量：120克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 正在查询药品库存 - 药品：63040001YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 开始查询库存 - 药品：63040001YP1，规格：1g，厂家：重庆康嘉，药房：110202
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 创建数据库连接...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 构建SQL查询...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] SQL语句：SELECT NVL(SUM(QUANTITY), 0)  FROM DRUG_STOCK   WHERE DRUG_CODE = :DRUGCODE  AND PACKAGE_SPEC = :DRUGSPEC  AND FIRM_ID = :FRIMID AND STORAGE = :DISPENSARY AND SUPPLY_INDICATOR = '1'  AND HIS_UNIT_CODE = :HIS_UNIT_CODE
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 设置查询参数...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 参数值 - DRUGCODE:63040001YP1, DRUGSPEC:1g, FRIMID:重庆康嘉, DISPENSARY:110202, HIS_UNIT_CODE:45038900950011711A6001
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 执行数据库查询...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 查询执行完成
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 处理查询结果...
[2025-08-28 11:54:50] [库存验证] [GetDrugInventoryInStorage] 查询成功 - 药品：63040001YP1，库存数量：2453
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 库存查询结果 - 药品：63040001YP1，库存数量：2453
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 临时简化验证 - 药品：菊花(63040001YP1)，物理库存：2453，预扣库存：0（已禁用），可用库存：2453
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 库存验证通过 - 药品：菊花(63040001YP1)
[2025-08-28 11:54:50] [库存验证] [ValidateStock] 物理库存：2453.00克，预扣库存：0.00克，可用库存：2453.00克，需要数量：120.00克
[2025-08-28 11:54:50] [库存验证] [ValidateStock] ========== 验证结束（成功）==========
